//////////////////////////////////////////////////////////////////////////////
//
// File to simulate process p p-> jj + X  with Jet Energy Correlator Measured ////
//
/////////////////////////////////////////////////////////////////////////////
#include "Pythia8/Plugins.h"
#include "Pythia8/Pythia.h"

#include <algorithm>
#include <cmath>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <vector>

// #include "bin_generator.h"

namespace fs = std::filesystem;
using namespace Pythia8;
using namespace std;

// wait on enter, for debugging.
inline void wait_on_enter() {
    std::string dummy;
    std::cout << "Enter to continue..." << std::endl;
    std::getline(std::cin, dummy);
}

// Uses binary search to find the bin index for a given value.
inline int find_bin_index(const vector<double>& edges, double value) {
    // std::upper_bound finds the first element in the range [first, last) which compares greater than val.
    auto it = std::upper_bound(edges.begin(), edges.end(), value);

    // If the value is smaller than the first edge, it's out of range.
    if (it == edges.begin()) {
        return -1;
    }
    // If the value is larger than or equal to the last edge, it's out of range.
    if (it == edges.end()) {
        return -1;
    }
    // The index is the distance to the found iterator, minus one.
    return std::distance(edges.begin(), it) - 1;
}


int main(int argc, char* argv[]) {

    if (argc < 13) {
        cerr << "Usage: " << argv[0] << " [eCM] [channel] [ptlow] [ptupp] [n_pt_bins] [eta] [pow1] [pow2] [chargeflag] [nEvent] [seed] [alphaS_MZ] [path]" << endl;
        return 1;
    }

    // Configuaration parameters
    const double ecom          = stod(argv[1]);   // machine energy
    const int    chflag        = stoi(argv[2]);   // channels
    const double elow          = stod(argv[3]);   // jet pt lower bound
    const double eupp          = stod(argv[4]);   // jet pt upper bound
    const int    n_pt_bins     = stoi(argv[5]);   // NEW: Number of pT bins to split the range into
    const double eta           = stod(argv[6]);   // jet eta cut
    const double pow1          = stod(argv[7]);   // energy power z1^pow1
    const double pow2          = stod(argv[8]);   // energy power z2^pow2
    const int    chgflag       = stoi(argv[9]);   // all hadron ? track
    const int    nEvent        = stoi(argv[10]);  // number of events
    const string numofparallel = argv[11];        // seed
    const double alphaS_MZ     = stod(argv[12]);  // alpha_s at M_Z
    const string path          = argv[13];        // output path

    // Jet Parameters
    float  radius   = 0.4;
    double eta_pmax = 3.0;
    double eta_jmax = eta;
    double pTparMin = 1.0;
    double pTjetMin = elow * 0.8;
    double dphi     = 2.0;

    const int    num_bin  = 50;
    const double min_val  = 1e-3;
    const double max_val  = 1;

    vector<double> bin_edges;
    vector<double> bin_midpoints;
    vector<double> bin_widths;

    const double log_min = log10(min_val);
    const double log_max = log10(max_val);
    const double log_step = (log_max - log_min) / num_bin;

    bin_edges.resize(num_bin + 1);

    // Generate bin edges
    for (int i = 0; i <= num_bin; ++i) {
        bin_edges[i] = pow(10.0, log_min + i * log_step);
    }

    // Generate bin midpoints (geometric mean is natural for log bins)
    bin_midpoints.resize(num_bin);
    for (int i = 0; i < num_bin; ++i) {
        bin_midpoints[i] = sqrt(bin_edges[i] * bin_edges[i+1]);
    }


    vector<vector<double>> ybins(n_pt_bins, vector<double>(bin_edges.size() - 1, 0.0));

    vector<double> pt_bin_edges;

    if (n_pt_bins <= 0) {
        cerr << "Error: Number of pT bins must be a positive integer." << endl;
        return 1;
    }
    const double pt_step = (eupp - elow) / n_pt_bins;
    for (int i = 0; i <= n_pt_bins; ++i) {
        pt_bin_edges.push_back(elow + i * pt_step);
    }

    vector<unsigned long>  jetCounters(n_pt_bins, 0);
    vector<double>         check_sums(n_pt_bins, 0.0);

    // Initialize Pythia
    Pythia        pythia;
    Event&        event        = pythia.event;
    Settings&     settings     = pythia.settings;
    ParticleData& particleData = pythia.particleData;

    // Set up pp beams and center of mass energy
    pythia.readString("Beams:frameType = 1");
    pythia.readString("Beams:idA = 2212");
    pythia.readString("Beams:idB = 2212");

    pythia.readString("PDF:pSet = LHAPDF6:NNPDF31_nlo_as_0118");

    pythia.settings.parm("Beams:eCM", ecom);

    // set alpha_s at M_Z, and two loop running for the process
    pythia.settings.parm("SigmaProcess:alphaSvalue", alphaS_MZ);
    pythia.readString("SigmaProcess:alphaSorder = 2");
    ////also set alpha_s, and two loop running and CMW scheme (so that it gets the two loop cusp) for the shower.
    pythia.settings.parm("TimeShower:alphaSvalue", alphaS_MZ);
    pythia.readString("TimeShower:alphaSorder = 2");
    pythia.readString("TimeShower:alphaSuseCMW = on");
    pythia.settings.parm("SpaceShower:alphaSvalue", alphaS_MZ);
    pythia.readString("SpaceShower:alphaSorder = 2");
    pythia.readString("SpaceShower:alphaSuseCMW = on");

    std::cout << "DEBUG: The value of chflag right before switch is: " << chflag << std::endl;

    // Interaction mechanism.
    switch (chflag) {
    case 0:
        pythia.readString("HardQCD:all = on");
        pythia.readString("HardQCD:nQuarkNew = 5");
        break;
    case 1:
        pythia.readString("WeakBosonAndParton:qqbar2gmZg = on");
        pythia.readString("WeakBosonAndParton:qg2gmZq = on");
        break;
    case 2:
        pythia.readString("WeakBosonAndParton:qg2gmZq = on");
        break;
    case 3:
        pythia.readString("WeakBosonAndParton:qqbar2gmZg = on");
        break;
    default:
        cerr << "Invalid channel selection!" << endl;
        return 1;
    }

    // pythia.readString("PhaseSpace:mHatMin = 0.");
    // double pTcut = pTjetMin*0.8;
    pythia.settings.parm("PhaseSpace:pTHatMin", pTjetMin);

    // QED radiation off lepton not handled yet by the new procedure.
    pythia.readString("PDF:lepton = off");
    pythia.readString("TimeShower:QEDshowerByL = off");

    pythia.readString("PartonLevel:MPI = off");
    // pythia.readString("PartonLevel:Remnants = off");
    // pythia.readString("PartonLevel:ISR = off");
    // pythia.readString("PartonLevel:FSR = off");

    pythia.readString("HadronLevel:all = on");
    // pythia.readString("HadronLevel:Hadronize = off");
    // pythia.readString("HadronLevel:Decay = off");

    // make charged hadron not decay, so we can observe them in final-state
    // pi0: 111 pi+: 211 K+: 321 p: 2212 n: 2112
    // it also turn off the anti-particle of the above
    int notDecay[5] = { 111, 211, 321, 2212, 2112 };
    for (int iC = 0; iC < 5; ++iC) {
        particleData.mayDecay(notDecay[iC], false);
    }
    // turn off Z decay
    pythia.particleData.mayDecay(23, false);

    // print out information
    // pythia.readString("Next:numberCount = 0");
    // pythia.readString("Next:numberShowInfo = 10");
    // pythia.readString("Next:numberShowProcess = 10");
    // pythia.readString("Next:numberShowEvent = 10");

    // use random seed
    // pythia.readString("Random:setSeed = on");
    // pythia.readString("Random:seed = 0");
    pythia.readString("Random:setSeed = on");
    pythia.readString(("Random:seed = " + numofparallel).c_str());

    string folder        = to_string((int)elow) + "-" + to_string((int)eupp) + "GeV" + "_" + to_string((double)alphaS_MZ);
    string data_dir_path = path + "/data/" + folder + "/";
    try {
        fs::create_directories(data_dir_path);
        cout << "Created directory: " << data_dir_path << endl;
    }
    catch (const fs::filesystem_error& e) {
        cerr << "Error creating directory: " << e.what() << endl;
        return 1;
    }

    ofstream file1(path + "/data/" + folder + "/q2z.txt", ios::trunc);  // save q2z bin
    for (int j = 0; j < bin_edges.size() - 1; j++) {
        bin_widths.push_back(bin_edges[j + 1] - bin_edges[j]);
        file1 << fixed << setprecision(6) << bin_midpoints[j] << endl;
    }
    file1.close();
    cout << "q2z bin saved to q2z.txt." << endl;
    cout << endl;

    // save data for every 50000 events created
    const int output_interval = 50000;
    SlowJet   slowJet(-1, radius, pTjetMin, eta_jmax);
    pythia.init();

    for (int iEvent = 0; iEvent < nEvent; ++iEvent) {

        if (!pythia.next())
            continue;
        double weight = pythia.info.weight();

        slowJet.analyze(pythia.event);

        int nJet = slowJet.sizeJet();

        // demand at least jets
        int nLeadJet;
        switch (chflag) {
        case 0:
            nLeadJet = 2;
            break;
        case 1:
            nLeadJet = 1;
            break;
        case 2:
            nLeadJet = 1;
            break;
        case 3:
            nLeadJet = 1;
            break;
        default:
            return 1;
        }

        // cout << nJet << " jets found in event " << iEvent << endl;

        if (nJet < nLeadJet)
            continue;

        // leading 2-jet requirements, follow cms
        double phijet1 = slowJet.p(0).phi();
        double phijet2;
        if (nLeadJet == 2)
            phijet2 = slowJet.p(1).phi();
        if (nLeadJet == 1)
            phijet2 = event[5].phi();

        if (abs(phijet1 - phijet2) < dphi)
            continue;  // close to b2b configuration

        // loop over leading jets
        // for (int iJet = 0; iJet < nLeadJet; iJet++) {
        for (int iJet = 0; iJet < nLeadJet; iJet++) {
            double EJet   = slowJet.p(iJet).e();
            double etaJet = slowJet.p(iJet).eta();
            double pTJet  = slowJet.p(iJet).pT();

            if (pTJet < elow || pTJet > eupp)
                continue;
            if (abs(etaJet) > eta_jmax)
                continue;

            // Find which pT sub-bin this jet belongs to
            int pt_bin_index = static_cast<int>((pTJet - elow) / pt_step);

            if (pt_bin_index < 0 || pt_bin_index >= n_pt_bins)
                continue;

            ++jetCounters[pt_bin_index];

            vector<int> constituents = slowJet.constituents(iJet);

            for (int iPart = 0; iPart < slowJet.multiplicity(iJet); iPart++) {

                if (iPart >= constituents.size())
                    continue;

                double ei      = event[constituents[iPart]].e();
                double pti     = event[constituents[iPart]].pT();
                double etai    = event[constituents[iPart]].eta();
                double phii    = event[constituents[iPart]].phi();
                double chargei = event[constituents[iPart]].charge();
                double zi      = ei / EJet;

                if (pti < pTparMin)
                    continue;

                // no counter term included
                for (int jPart = iPart; jPart < constituents.size(); jPart++) {

                    if (jPart >= constituents.size())
                        continue;

                    double ej      = event[constituents[jPart]].e();
                    double ptj     = event[constituents[jPart]].pT();
                    double etaj    = event[constituents[jPart]].eta();
                    double phij    = event[constituents[jPart]].phi();
                    double chargej = event[constituents[jPart]].charge();

                    double zj = ej / EJet;

                    if (ptj < pTparMin)
                        continue;

                    double zizj   = .5 * (pow(zi, pow1) * pow(zj, pow2) + pow(zi, pow2) * pow(zj, pow1));
                    double RLij   = (etai - etaj) * (etai - etaj) + (phii - phij) * (phii - phij);
                    double q2zeec = pTJet * pTJet * RLij / 2. / 2. * 4.;

                    // factor of 2 account for ji configuration
                    if (chgflag == 0 && event[constituents[iPart]].isHadron() && event[constituents[iPart]].isFinal() && event[constituents[jPart]].isHadron()
                        && event[constituents[jPart]].isFinal()) {
                        int bin_index = find_bin_index(bin_edges, q2zeec);
                        if (iPart == jPart)
                            check_sums[pt_bin_index] += 1.0 * zizj * weight;
                        else
                            check_sums[pt_bin_index] += 2.0 * zizj * weight;

                        if (bin_index >= 0 && bin_index < bin_edges.size() - 1) {
                            ybins[pt_bin_index][bin_index] += 2.0 * zizj * weight;
                        }
                    }
                    else if (chgflag == 1
                             && (event[constituents[iPart]].isCharged() && event[constituents[jPart]].isCharged() && event[constituents[iPart]].isHadron() && event[constituents[jPart]].isHadron())) {
                        int bin_index = find_bin_index(bin_edges, q2zeec);
                        if (bin_index >= 0 && bin_index < bin_edges.size() - 1) {
                            ybins[pt_bin_index][bin_index] += abs(chargei * chargej) * 2.0 * zizj * weight;
                        }
                    }
                }
            }

            if ((iEvent + 1) % output_interval == 0 || iEvent == nEvent - 1) {
                cout << "--- Periodic Save at Event " << iEvent + 1 << "/" << nEvent << " ---" << endl;

                for (int k = 0; k < n_pt_bins; ++k) {
                    double current_pt_low = pt_bin_edges[k];
                    double current_pt_upp = pt_bin_edges[k + 1];

                    string outPath;
                    string pt_range_str = "_pt_" + to_string((int)current_pt_low) + "-" + to_string((int)current_pt_upp);
                    string base_name;

                    string common_name_part = to_string((double)ecom) + "GeV" + pt_range_str + "GeV_eta" + to_string((double)eta);
                    string end_name_part    = "_Charge_" + to_string(chgflag) + "_E" + to_string((int)pow1) + "E" + to_string((int)pow2) + "C_run_" + numofparallel + "_log.txt";

                    switch (chflag) {
                    case 0:
                    case 1:
                        base_name = common_name_part + "_all_parton_" + to_string(chflag) + end_name_part;
                        break;
                    case 2:
                        base_name = common_name_part + "_quark_parton_" + to_string(chflag) + end_name_part;
                        break;
                    case 3:
                        base_name = common_name_part + "_gluon_parton_" + to_string(chflag) + end_name_part;
                        break;
                    default:
                        continue;  // Should not happen
                    }
                    outPath = data_dir_path + base_name;

                    double current_jetCounter = jetCounters[k];
                    double current_normFactor = current_jetCounter > 0 ? 1.0 / current_jetCounter : 0.0;

                    ofstream file2(outPath.c_str(), ios::trunc);
                    if (!file2.is_open()) {
                        cerr << "Error: Could not open file for periodic save: " << outPath << endl;
                        continue;  // 如果打开失败，继续处理下一个pT子区间
                    }

                    for (size_t jt = 0; jt < ybins[k].size(); ++jt) {
                        file2 << fixed << setprecision(12) << ybins[k][jt] * current_normFactor / bin_widths[jt] << endl;
                    }
                    file2.close();
                }
                cout << "--- Periodic Save Complete for all pT bins ---" << endl;
            }
        }
    }
    pythia.stat();
    // Final output generation now loops over all pT bins and creates a file for each.
    cout << endl << "====== Generating Output Files ======" << endl;

    for (int k = 0; k < n_pt_bins; ++k) {
        double current_pt_low = pt_bin_edges[k];
        double current_pt_upp = pt_bin_edges[k + 1];

        // Construct a unique file name for each pT bin
        string outPath;
        string pt_range_str = "_pt_" + to_string((int)current_pt_low) + "-" + to_string((int)current_pt_upp);
        string base_name;

        // Reconstruct base name based on channel
        string common_name_part = to_string((double)ecom) + "GeV" + pt_range_str + "GeV_eta" + to_string((double)eta);
        string end_name_part    = "_Charge_" + to_string(chgflag) + "_E" + to_string((int)pow1) + "E" + to_string((int)pow2) + "C_run_" + numofparallel + "_log.txt";

        switch (chflag) {
        case 0:
        case 1:
            base_name = common_name_part + "_all_parton_" + to_string(chflag) + end_name_part;
            break;
        case 2:
            base_name = common_name_part + "_quark_parton_" + to_string(chflag) + end_name_part;
            break;
        case 3:
            base_name = common_name_part + "_gluon_parton_" + to_string(chflag) + end_name_part;
            break;
        default:
            continue;  // Should not happen
        }
        outPath = data_dir_path + base_name;

        cout << "Processing pT bin: [" << current_pt_low << ", " << current_pt_upp << "] GeV" << endl;
        cout << " -> Output file: " << outPath << endl;

        double Norm       = jetCounters[k];
        double normFactor = (Norm > 0) ? 1.0 / Norm : 0.0;

        cout << " -> Jet counts: " << (unsigned long)Norm << endl;

        ofstream file2(outPath.c_str(), ios::trunc);
        for (size_t j = 0; j < ybins[k].size(); j++) {
            file2 << fixed << setprecision(12) << ybins[k][j] * normFactor / bin_widths[j] << endl;
        }
        file2.close();

        cout << " -> Check sum: " << check_sums[k] * normFactor << endl << endl;
    }

    cout << "========= Done! All data saved! ==========" << endl;

    return 0;
}
