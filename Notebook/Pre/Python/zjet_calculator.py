import numpy as np
from enum import Enum, auto
# 假设这些文件都在同一个目录下
# from phase_space_zjet import PhaseSpaceZJet
from ALZ_running import AlphaSRunner

# 枚举类保持不变
class ZJetChannel(Enum):
    Q_JET = auto()
    G_JET = auto()

class ZJetIntegrand:
    """
    为 Vegas 积分优化的可调用对象。
    现在支持动态的 alpha_s 计算。
    """
    def __init__(self, channel: ZJetChannel, 
                 alpha_s_runner: AlphaSRunner,
                 scale_variation: float = 1.0,
                 mz: float = 91.2, gz: float = 0.81):
        """
        在初始化时配置好固定参数。

        Args:
            channel (ZJetChannel): 计算的通道。
            alpha_s_runner (AlphaSRunner): 一个 AlphaSRunner 类的实例。
            kappa (float): 能量标度因子，最终标度为 mu = kappa * pT。
        """
        print(f"--- Integrand configured for channel={channel.name}, scale factor kappa={kappa} ---")
        self.channel = channel
        self.alpha_s_runner = alpha_s_runner  # 存储 runner 对象
        self.kappa = kappa                    # 存储标度因子
        self.MZ = mz
        self.GZ = gz
        self.phase_space = PhaseSpaceZJet

    def __call__(self, x: np.ndarray) -> float:
        """
        这个方法让类的实例表现得像一个函数。
        """
        v, w, zc, pT, y = x

        shat = self.phase_space.shat(v, w, zc, pT, y)
        if shat <= 0:
            return 0.0
            
        # 💡 修改点 2: 在每个积分点动态计算 alpha_s
        # 我们选择能量标度 mu = kappa * pT
        scale = self.kappa * pT
        # 通过存储的 runner 对象计算 alpha_s
        alpha_s = self.alpha_s_runner.alpha_s(scale)

        uhat = self.phase_space.uhat(v, w, zc, pT, y)
        that = self.phase_space.that(v, w, zc, pT, y)
        if uhat == 0 or that == 0:
            return 0.0

        matrix_element = 0.0
        if self.channel == ZJetChannel.Q_JET:
            numerator = shat**2 + uhat**2 - 2.0 * self.MZ**2 * that
            matrix_element = -1.0 / 12.0 * (numerator / (shat * uhat))
        elif self.channel == ZJetChannel.G_JET:
            numerator = that**2 + uhat**2 + 2.0 * self.MZ**2 * shat
            matrix_element = 2.0 / 9.0 * (numerator / (that * uhat))
            
        # 💡 修改点 3: 使用动态计算出的 alpha_s
        result = matrix_element * (alpha_s / (8.0 * shat)) * self.GZ**4
        
        return result

# --- Vegas 积分示例 ---
if __name__ == "__main__":
    import vegas

    # 1. ✅ 创建 AlphaSRunner 的实例
    #    在你的项目中，这里会是: runner = AlphaSRunner()
    runner = AlphaSRunner()

    # 2. ✅ 配置被积函数：将 runner 实例传递进去
    #    这里我们还可以设置 kappa，比如 kappa=0.5 来研究标度不确定性
    integrand_qjet = ZJetIntegrand(
        channel=ZJetChannel.Q_JET, 
        alpha_s_runner=runner,
        kappa=1.0  # 你可以改成 0.5 或 2.0 来测试
    )

    # 3. 定义积分区间 (保持不变)
    integration_bounds = [
        [0, 1],      # v
        [0, 1],      # w
        [0, 1],      # zc
        [20, 40],    # pT (GeV)
        [-0.5, 0.5]  # y (rapidity)
    ]
    
    # 4. 创建 vegas 积分器并执行积分
    integ = vegas.Integrator(integration_bounds)
    result = integ(integrand_qjet, nitn=10, neval=20000)

    print("\n--- Vegas Integration Result ---")
    print(result.summary())
    print(f"Result for Q_JET channel (kappa={integrand_qjet.kappa}) = {result}")