import math

class PhaseSpaceZJet:
    """
    A class to calculate phase space variables for Z+jet production.
    The beam energy (q_beam) is a required parameter for instantiation.
    """
    def __init__(self, Ecom: float=14000):
        """
        Initializes the phase space calculator with a specific beam energy.

        Args:
            q_beam (float): The energy of a single beam in GeV.
        """
        self.Q_BEAM = Ecom/2

        # Physical constants are now instance attributes
        self.MZ = 91.2
        self.GZ = 0.81
        self.SIN2_EFF = 0.23122

    def s(self) -> float:
        """Calculates the total hadronic center-of-mass energy squared."""
        return 4.0 * self.Q_BEAM**2

    def t(self, pt: float, eta: float) -> float:
        """Calculates the Mandelstam variable t."""
        return -2 * self.Q_BEAM * (pt * math.cosh(eta) - pt * math.sinh(eta))

    def u(self, pt: float, eta: float) -> float:
        """Calculates the Mandelstam variable u."""
        return -2 * self.Q_BEAM * (pt * math.cosh(eta) + pt * math.sinh(eta))

    def v_var(self, pt: float, eta: float) -> float:
        """Calculates the dimensionless variable V."""
        return 1 + self.t(pt, eta) / self.s()

    def w_var(self, pt: float, eta: float) -> float:
        """Calculates the dimensionless variable W."""
        return -self.u(pt, eta) / (self.s() + self.t(pt, eta))

    def x1(self, v: float, w: float, zc: float, pt: float, eta: float) -> float:
        """Calculates the momentum fraction x1."""
        return self.v_var(pt, eta) * self.w_var(pt, eta) / (v * w * zc)

    def x2(self, v: float, w: float, zc: float, pt: float, eta: float) -> float:
        """Calculates the momentum fraction x2."""
        return (1 - self.v_var(pt, eta)) / (zc * (1 - v))

    def shat(self, v: float, w: float, zc: float, pt: float, eta: float) -> float:
        """Calculates the partonic center-of-mass energy squared."""
        x1_val = self.x1(v, w, zc, pt, eta)
        x2_val = self.x2(v, w, zc, pt, eta)
        return x1_val * x2_val * self.s()

    def that(self, v: float, w: float, zc: float, pt: float, eta: float) -> float:
        """Calculates the partonic Mandelstam variable t-hat."""
        x1_val = self.x1(v, w, zc, pt, eta)
        return x1_val * self.t(pt, eta) / zc

    def uhat(self, v: float, w: float, zc: float, pt: float, eta: float) -> float:
        """Calculates the partonic Mandelstam variable u-hat."""
        x2_val = self.x2(v, w, zc, pt, eta)
        return x2_val * self.u(pt, eta) / zc

    def vq_plus_aq(self, quark_flavor: int) -> float:
        """Calculates the squared electroweak couplings (independent of beam energy)."""
        if quark_flavor in [1, 3, 5]:  # d, s, b
            term1 = -0.5 - (2.0 / 3.0) * self.SIN2_EFF
            return term1**2 + (-0.5)**2
        elif quark_flavor in [2, 4]:  # u, c
            term1 = 0.5 + (4.0 / 3.0) * self.SIN2_EFF
            return term1**2 + (0.5)**2
        elif quark_flavor == 6:
            raise ValueError("Invalid argument: No top quark.")
        else:
            raise ValueError(f"Unknown quark flavor: {quark_flavor}")

if __name__ == "__main__":
    # --- Example Usage ---
    
    # 1. Create an instance for LHC energies
    ps_lhc = PhaseSpaceZJet(q_beam=7000.0)
    print(f"--- Testing for LHC (Q_beam = {ps_lhc.Q_BEAM} GeV) ---")
    
    # Call methods on the instance
    s_val_lhc = ps_lhc.s()
    print(f"s = {s_val_lhc:.4e}")

    # 2. Create another instance for a different collider energy
    ps_rhic = PhaseSpaceZJet(q_beam=255.0)
    print(f"\n--- Testing for RHIC (Q_beam = {ps_rhic.Q_BEAM} GeV) ---")
    s_val_rhic = ps_rhic.s()
    print(f"s = {s_val_rhic:.4e}")

    # The coupling calculation is independent of beam energy but can be called from any instance
    u_coupling = ps_lhc.vq_plus_aq(2)
    print(f"\nUp quark coupling (same for all energies): {u_coupling:.4f}")