from ALZ_running import AlphaSRunner

from phase_space_zjet import PhaseSpaceZJet as PhaseSpaceCalculator
from evolution_matrix import EvolutionMatrix

import os
import multiprocessing
import numpy as np
import lhapdf
import vegas
from typing import Tuple
from enum import Enum, auto


def read_eec_extract_result(filename):
    """
    读取EEC_Extract.py输出的txt文件，获取拟合参数

    Args:
        filename: EEC_Extract.py输出的结果文件路径

    Returns:
        tuple: (fit, fit_NLO, pt_center, 其他参数字典)
    """
    if not os.path.exists(filename):
        raise FileNotFoundError(f"EEC结果文件不存在: {filename}")

    with open(filename, 'r') as f:
        lines = f.readlines()

    # 找到最后一个非注释行
    last_line = None
    for line in reversed(lines):
        line = line.strip()
        if line and not line.startswith('#'):
            last_line = line
            break

    if last_line is None:
        raise ValueError("文件中没有找到数据行")

    # 解析数据
    values = last_line.split()
    if len(values) != 11:
        raise ValueError(f"数据行格式错误，期望11个值，实际得到{len(values)}个")

    try:
        # 格式: Ecom radius pt_center eta_min eta_max kappa_R kappa_F LO_fit_0 LO_fit_1 NLO_fit_0 NLO_fit_1 result_12 result_13
        Ecom = float(values[0])
        radius = float(values[1])
        pt_center = float(values[2])
        eta_min = float(values[3])
        eta_max = float(values[4])
        kappa_R = float(values[5])
        kappa_F = float(values[6])
        LO_fit_0 = float(values[7])
        LO_fit_1 = float(values[8])
        NLO_fit_0 = float(values[9])
        NLO_fit_1 = float(values[10])

        # 创建拟合参数数组
        fit = np.array([LO_fit_0, LO_fit_1])
        fit_NLO = np.array([NLO_fit_0, NLO_fit_1])

        # 其他参数
        params = {
            'Ecom': Ecom,
            'radius': radius,
            'eta_range': (eta_min, eta_max),
            'kappa_R': kappa_R,
            'kappa_F': kappa_F,
        }

        return fit, fit_NLO, pt_center, params

    except ValueError as e:
        raise ValueError(f"数据转换错误: {e}")

# 定义一个进程本地缓存，用于安全地在多进程环境中初始化和存储LHAPDF对象。
_PDF_CACHE = {}

os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"


class Channel(Enum):
    qiqj_qX = auto()
    qiqbarj_qX = auto()
    qiqbari_qjX = auto()
    qiqi_qX = auto()
    qiqbari_qiX = auto()
    qiqbari_gX = auto()
    qg_qX = auto()
    qg_gX = auto()
    gg_gX = auto()
    gg_qX = auto()


class PPJet_VegasIntegrand:
    NC = 3.0
    CF = (NC**2 - 1.0) / (2.0 * NC)

    QUARK_FLAVORS = [1, 2, 3, 4, 5]
    ANTI_QUARK_FLAVORS = [-1, -2, -3, -4, 5]
    NF = float(len(QUARK_FLAVORS))

    ALL_CHANNELS = list(Channel)

    def __init__(
        self,
        pdf_name: str,
        ALZ_runner: AlphaSRunner,
        Ecom: float,
        Q0: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        R: float,
        kappa_R: float = 1.0,
        kappa_F: float = 1.0,
    ):

        self.pdf_name = pdf_name
        self.ALZ_runner = ALZ_runner
        self.Q0 = Q0
        self.kappa_R = kappa_R
        self.kappa_F = kappa_F
        self.pT_range = pT_range
        self.eta_range = eta_range
        self.R = R
        self.phase_space = PhaseSpaceCalculator(Ecom=Ecom)
        self.num_channels = len(self.ALL_CHANNELS)

        self.eec_evolver_LL = EvolutionMatrix(
            order=0, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )
        self.eec_evolver_NLL = EvolutionMatrix(
            order=1, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )
        self.eec_evolver_NNLL = EvolutionMatrix(
            order=2, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )

    def __call__(self, y: np.ndarray) -> np.ndarray:
        pid = os.getpid()
        if pid not in _PDF_CACHE:
            lhapdf.setVerbosity(0)
            _PDF_CACHE[pid] = lhapdf.mkPDF(self.pdf_name)
        pdf = _PDF_CACHE[pid]

        y = np.atleast_2d(y)
        N = y.shape[0]
        # Output is now a single value (total cross section), so shape is (N,).
        out = np.empty((N, 16), dtype=float)

        for i in range(N):
            out[i] = self._single_event(y[i, 0], y[i, 1], y[i, 2], pdf)
        return out

    def _calculate_luminosity(self, channel: Channel, pdf, x1, x2, mu):
        """Translates the C++ pdf_prd function."""
        lumi = 0.0

        # Use a small epsilon for float comparison
        if x1 <= 0 or x2 <= 0:
            return 0.0

        # Helper lambda to match C++ 'pdf_pair'
        pdf_pair = lambda i, j: pdf.xfxQ(i, x1, mu) * pdf.xfxQ(j, x2, mu)

        # All quark and anti-quark flavors
        all_quarks = self.QUARK_FLAVORS + self.ANTI_QUARK_FLAVORS
        g = 21

        if channel == Channel.qiqj_qX:  # q_i q_j
            for i in all_quarks:
                for j in all_quarks:
                    if abs(i) != abs(j):
                        lumi += pdf_pair(i, j)

        elif channel == Channel.qiqbarj_qX:  # q_i qbar_j
            for i in self.QUARK_FLAVORS:
                for j in self.QUARK_FLAVORS:
                    if i != j:
                        lumi += pdf_pair(i, -j) + pdf_pair(-i, j)

        elif channel == Channel.qiqbari_qjX:  # q_i qbar_i (for q_j qbar_j final state)
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qiqi_qX:  # q_i q_i
            for i in all_quarks:
                lumi += pdf_pair(i, i)

        elif channel == Channel.qiqbari_qiX:  # q_i qbar_i (for q_i qbar_i final state)
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qiqbari_gX:  # q_i qbar_i (for g g final state)
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qg_qX or channel == Channel.qg_gX:  # q g
            for i in all_quarks:
                lumi += pdf_pair(i, g) + pdf_pair(g, i)

        elif channel == Channel.gg_gX or channel == Channel.gg_qX:  # g g
            lumi += pdf_pair(g, g)

        return lumi

    def _calculate_matrix_element_sq(self, channel: Channel, v: float):
        """
        Translates the matrix element part of the C++ dsigma_dvdw function.
        Strictly follows the C++ implementation without simplification.
        """
        # Guard against division by zero or invalid v
        epsilon = 1e-9
        if v <= epsilon or v >= 1.0 - epsilon:
            return 0.0

        v2 = v * v
        # v3 = v2 * v
        # v4 = v2 * v2

        one_minus_v = 1.0 - v
        one_minus_v_sq = one_minus_v * one_minus_v

        # Abbreviate for convenience
        NC, CF, NF = self.NC, self.CF, self.NF

        matrix_element_sq = 0.0

        if channel == Channel.qiqj_qX or channel == Channel.qiqbarj_qX:
            # C++: CF / NC * (1 + pow(v, 2)) / pow(1 - v, 2)
            term_v = CF / NC * (1 + v2) / one_minus_v_sq
            # C++: CF / NC * (1 + pow(1 - v, 2)) / pow(v, 2)
            term_one_minus_v = CF / NC * (1 + one_minus_v_sq) / v2
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_qjX:
            # C++: CF / NC * (1.0 + 2 * pow(v, 2) - 2 * v) * (NF - 1.0)
            term_v = CF / NC * (1.0 + 2 * v2 - 2 * v) * (NF - 1.0)
            # C++: CF / NC * (1.0 + 2 * pow(1 - v, 2) - 2 * (1 - v)) * (NF - 1.0)
            term_one_minus_v = (
                CF / NC * (1.0 + 2 * one_minus_v_sq - 2 * one_minus_v) * (NF - 1.0)
            )
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqi_qX:
            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(v, 4) - ... ) / pow(1 - v, 2) / pow(v, 2) / 2.0
            num_v = (
                NC * v**4
                - 2.0 * NC * v**3
                + 4.0 * NC * v2
                + v2
                - (3.0 * NC + 1) * v
                + NC
            )
            term_v = (2.0 * CF / (NC**2)) * num_v / one_minus_v_sq / v2 / 2.0

            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(1 - v, 4) - ... ) / pow(v, 2) / pow(1 - v, 2) / 2.0
            num_one_minus_v = (
                NC * one_minus_v**4
                - 2.0 * NC * one_minus_v**3
                + 4.0 * NC * one_minus_v_sq
                + one_minus_v_sq
                - (3.0 * NC + 1) * one_minus_v
                + NC
            )
            term_one_minus_v = (
                (2.0 * CF / (NC**2)) * num_one_minus_v / v2 / one_minus_v_sq / 2.0
            )

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_qiX:
            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(v, 4) - (3.0 * NC + 1.0) * pow(v, 3) + ...) / pow(1 - v, 2)
            num_v = (
                NC * v**4
                - (3.0 * NC + 1.0) * v**3
                + (4 * NC + 1.0) * v2
                - (2.0 * NC) * v
                + NC
            )
            term_v = (2.0 * CF / (NC**2)) * num_v / one_minus_v_sq

            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(1 - v, 4) - (3.0 * NC + 1.0) * pow(1 - v, 3) + ...) / pow(v, 2)
            num_one_minus_v = (
                NC * one_minus_v**4
                - (3.0 * NC + 1.0) * one_minus_v**3
                + (4 * NC + 1.0) * one_minus_v_sq
                - (2.0 * NC) * one_minus_v
                + NC
            )
            term_one_minus_v = (2.0 * CF / (NC**2)) * num_one_minus_v / v2

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_gX:
            # C++ term_v part 1: CF / pow(NC, 2) * (2.0 * pow(v, 2) - 2.0 * v + 1.0)
            p1_v = CF / (NC**2) * (2.0 * v2 - 2.0 * v + 1.0)
            # C++ term_v part 2: (2.0 * pow(NC, 2) * pow(v, 2) - ... - 1.0)
            p2_v = 2.0 * (NC**2) * v2 - 2.0 * (NC**2) * v + (NC**2) - 1.0
            # C++ term_v full: ... / v / (1 - v) / 2.0
            term_v = p1_v * p2_v / v / one_minus_v / 2.0

            # C++ term_one_minus_v (similar structure)
            p1_omv = CF / (NC**2) * (2.0 * one_minus_v_sq - 2.0 * one_minus_v + 1.0)
            p2_omv = (
                2.0 * (NC**2) * one_minus_v_sq
                - 2.0 * (NC**2) * one_minus_v
                + (NC**2)
                - 1.0
            )
            term_one_minus_v = p1_omv * p2_omv / one_minus_v / v / 2.0

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qg_qX:
            # C++: (pow(v, 2) + 1.0) * ((pow(NC, 2) - 1.0) * pow(v, 2) + 2.0 * v + pow(NC, 2) - 1.0) / 2.0 / pow(NC, 2) / v / pow(1 - v, 2)
            num = (v2 + 1.0) * ((NC**2 - 1.0) * v2 + 2.0 * v + NC**2 - 1.0)
            matrix_element_sq = num / (2.0 * (NC**2) * v * one_minus_v_sq)

        elif channel == Channel.qg_gX:
            # C++: (pow(1 - v, 2) + 1.0) * ((pow(NC, 2) - 1.0) * pow(1 - v, 2) + 2.0 * (1 - v) + pow(NC, 2) - 1.0) / 2.0 / pow(NC, 2) / (1 - v) / pow(v, 2)
            num = (one_minus_v_sq + 1.0) * (
                (NC**2 - 1.0) * one_minus_v_sq + 2.0 * one_minus_v + NC**2 - 1.0
            )
            matrix_element_sq = num / (2.0 * (NC**2) * one_minus_v * v2)

        elif channel == Channel.gg_gX:
            # C++ term_v: (pow(pow(v, 2) - v + 1.0, 3) / pow(v, 2) / pow(1 - v, 2)) * 9.0 / 2.0 / 2.0
            term_v = (pow(v2 - v + 1.0, 3) / v2 / one_minus_v_sq) * 9.0 / 2.0 / 2.0

            # C++ term_one_minus_v: (pow(pow(1 - v, 2) - (1 - v) + 1.0, 3) / pow(1 - v, 2) / pow(v, 2)) * 9.0 / 2.0 / 2.0
            term_one_minus_v = (
                (pow(one_minus_v_sq - one_minus_v + 1.0, 3) / one_minus_v_sq / v2)
                * 9.0
                / 2.0
                / 2.0
            )

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.gg_qX:
            # C++ common factor: 1 / (2.0 * NC * (pow(NC, 2) - 1.0))
            common_pre = 1.0 / (2.0 * NC * (NC**2 - 1.0))

            # C++ term_v: ... * (pow(v, 2) + pow(1 - v, 2)) * (2.0 * pow(NC, 2) * (pow(v, 2) - v) + pow(NC, 2) - 1) / (1 - v) / v * (NF)
            num_v = (v2 + one_minus_v_sq) * (2.0 * NC**2 * (v2 - v) + NC**2 - 1.0)
            term_v = common_pre * num_v / one_minus_v / v * NF

            # C++ term_one_minus_v: ... * (pow(1 - v, 2) + pow(v, 2)) * (2.0 * pow(NC, 2) * (pow(1 - v, 2) - (1 - v)) + pow(NC, 2) - 1) / (1 - v) / v * (NF)
            num_omv = (one_minus_v_sq + v2) * (
                2.0 * NC**2 * (one_minus_v_sq - one_minus_v) + NC**2 - 1.0
            )
            term_one_minus_v = common_pre * num_omv / one_minus_v / v * NF

            matrix_element_sq = term_v + term_one_minus_v

        return matrix_element_sq

    def _single_event(self, y_pt, y_eta, y_v, pdf):

        channel_results = np.zeros(self.num_channels, dtype=float)
        zero_vector = channel_results.copy()
        
        pT = self.pT_range[0] + y_pt * (self.pT_range[1] - self.pT_range[0])
        eta = self.eta_range[0] + y_eta * (self.eta_range[1] - self.eta_range[0])
        jacobian = (self.pT_range[1] - self.pT_range[0]) * (
            self.eta_range[1] - self.eta_range[0]
        )
        zc = 1.0
        w = 1.0

        V = self.phase_space.v_var(pT, eta)
        W = self.phase_space.w_var(pT, eta)
        v_min = V * W / zc
        v_max = 1.0 - (1.0 - V) / zc
        if v_min >= 1.0 or v_max <= 0.0 or v_min >= v_max:
             return 0.0

        v = v_min + y_v * (v_max - v_min)
        jacobian *= v_max - v_min

        try:
            x1 = self.phase_space.x1(v, w, zc, pT, eta)
            x2 = self.phase_space.x2(v, w, zc, pT, eta)
            shat = self.phase_space.shat(v, w, zc, pT, eta)
        except Exception:
            return 0.0

        if not (0 < x1 < 1 and 0 < x2 < 1):
            return 0.0
        if shat <= 0:
            return 0.0

        mu_R = self.kappa_R * pT
        mu_F = self.kappa_F * pT
        alpha_s = self.ALZ_runner.alpha_s(mu_R)

        total_partonic_xs = 0.0
        for i, channel in enumerate(self.ALL_CHANNELS):
            lumi = self._calculate_luminosity(channel, pdf, x1, x2, mu_F)
            if lumi <= 0:
                continue

            me_sq = self._calculate_matrix_element_sq(channel, v)
            if me_sq <= 0:
                continue

            # Combine luminosity and matrix element
            total_partonic_xs += lumi * me_sq
            channel_results[i] = lumi * me_sq

        alpha_s_sq = alpha_s * alpha_s
        prefactor = (np.pi * alpha_s_sq / (2.0 * shat)) * 2 / (pT * zc**2)

        # Conversion factor GeV^-2 to pb
        conversion_factor = 0.3893792922e9

        q_cs = (
            (
                channel_results[0]
                + channel_results[1]
                + channel_results[2]
                + channel_results[3]
                + channel_results[4]
                + channel_results[6]
                + channel_results[9]
            )
            * jacobian
            * conversion_factor
            * prefactor
        )

        g_cs = (
            (channel_results[8] + channel_results[5] + channel_results[7])
            * jacobian
            * conversion_factor
            * prefactor
        )
        # g_cs= (channel_results[8])* jacobian * conversion_factor*prefactor

        dsig_vec = np.array([q_cs, g_cs])

        q_cs_NLO = q_cs * (
            1
            + alpha_s
            / 2
            / np.pi
            * AlphaSRunner.CF
            * (8131 / 450 - 13 / 2 + 91 / 30 * np.log(self.kappa_F * self.kappa_F))
        )

        g_cs_NLO = g_cs * (
            1
            + alpha_s
            / 2
            / np.pi
            * AlphaSRunner.CA
            * (
                411917 / 22050
                - 67 / 9
                - (33716 / 22050 / 2 * 5 - 23 / 9 / 2 * 5)
                + (-181 / 70 * AlphaSRunner.CA - 5 / 3)
                * np.log(self.kappa_F * self.kappa_F)
            )
        )
        final_dsig = prefactor * total_partonic_xs * jacobian * conversion_factor

        # q_cs_NLO = q_cs / final_dsig

        # g_cs_NLO = g_cs / final_dsig

        dsig_vec_NLO = np.array([q_cs_NLO, g_cs_NLO])

        Q0 = 50
        U_LL = self.eec_evolver_LL.get_evolution_operator(Q0, pT, self.R, m=0)
        U_NLL = self.eec_evolver_NLL.get_evolution_operator(Q0, pT, self.R, m=2)
        U_NNLL = self.eec_evolver_NNLL.get_evolution_operator(Q0, pT, self.R, m=4)

        A0_pow = self.eec_evolver_LL.get_operator_fit(Q0, self.R)

        A_LL_pow = U_LL @ A0_pow
        A_NLL_pow = U_NLL @ A0_pow
        A_NNLL_pow = U_NNLL @ A0_pow

        a = dsig_vec[0] * A_LL_pow[0][0]
        b = dsig_vec[0] * A_LL_pow[0][1]
        c = dsig_vec[1] * A_LL_pow[1][0]
        d = dsig_vec[1] * A_LL_pow[1][1]

        e = dsig_vec_NLO[0] * A_NLL_pow[0][0]
        f = dsig_vec_NLO[0] * A_NLL_pow[0][1]
        g = dsig_vec_NLO[1] * A_NLL_pow[1][0]
        h = dsig_vec_NLO[1] * A_NLL_pow[1][1]

        i = dsig_vec_NLO[0] * A_NNLL_pow[0][0]
        j = dsig_vec_NLO[0] * A_NNLL_pow[0][1]
        k = dsig_vec_NLO[1] * A_NNLL_pow[1][0]
        l = dsig_vec_NLO[1] * A_NNLL_pow[1][1]

        # The final result is d(sigma)/d(pT)d(eta)
        return np.array(
            [
                a,
                b,
                c,
                d,
                e,
                f,
                g,
                h,
                i,
                j,
                k,
                l,
                q_cs,
                g_cs,
                q_cs_NLO,
                g_cs_NLO,
            ]
        )


class PPJetCalculator:
    def __init__(self, pdf_name: str, ALS_MZ: float):
        print(f"正在初始化 pp->jet+X 截面计算器 (PDF: {pdf_name})...")
        try:
            lhapdf.setVerbosity(0)
            lhapdf.mkPDF(pdf_name)
        except Exception as e:
            print(f"错误: 无法加载 PDF 集 '{pdf_name}'.")
            raise e

        self.alpha_MZ = ALS_MZ
        self.pdf_name = pdf_name
        self.ALZ_runner = AlphaSRunner(ALS_MZ=self.alpha_MZ)
        print("初始化完成。")

    def calculate(
        self,
        Ecom: float,
        Q0: float,
        radius: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        scale_factors: Tuple[float, float] = (1.0, 1.0),
        vegas_params: dict = None,
    ) -> vegas.Integrator:
        if vegas_params is None:
            # Increased default neval for better precision in this complex integral
            vegas_params = {"nitn": 10, "neval": 100000}

        print(f"\n开始计算: pT范围 {pT_range} GeV, eta范围 {eta_range}...")
        print(f"标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")

        integrand = PPJet_VegasIntegrand(
            pdf_name=self.pdf_name,
            ALZ_runner=self.ALZ_runner,
            Ecom=Ecom,
            Q0=Q0,
            pT_range=pT_range,
            eta_range=eta_range,
            R=radius,
            kappa_R=scale_factors[0],
            kappa_F=scale_factors[1],
        )

        integration_bounds = [[0, 1], [0, 1], [0, 1]]
        integ = vegas.Integrator(integration_bounds)

        result = integ(integrand, **vegas_params)

        print("\n--- 计算结束 ---")
        return result


if __name__ == "__main__":
    # Set up multiprocessing for parallel VEGAS integration
    multiprocessing.set_start_method("spawn", force=True)

    # 解析命令行参数
    import argparse
    import sys

    parser = argparse.ArgumentParser(description='EEC演化计算程序')
    parser.add_argument('result_file', nargs='?', default="Extract_config/results.txt",
                       help='EEC_Extract.py的结果文件路径')
    parser.add_argument('-M', '--alpha-ratio', type=float, default=1.0,
                       help='alpha_s(MZ)的倍数 (默认: 1.0)')
    parser.add_argument('-r', '--scale-factor', type=float, default=1.0,
                       help='标度因子 (默认: 1.0)')
    parser.add_argument('-ptmin', type=float, default=None,
                       help='pT最小值 (GeV)')
    parser.add_argument('-ptmax', type=float, default=None,
                       help='pT最大值 (GeV)')
    parser.add_argument('-pt-bins', type=int, default=1,
                       help='pT区间个数 (默认: 1)')
    parser.add_argument('-pdf-name', type=str, default="NNPDF31_nlo_as_0118",
                       help='LHAPDF集名称 (默认: NNPDF31_nlo_as_0118)')

    args = parser.parse_args()

    eec_result_file = args.result_file
    alpha_ratio = args.alpha_ratio
    scale_factor = args.scale_factor

    print(f"命令行参数:")
    print(f"  结果文件: {eec_result_file}")
    print(f"  alpha_s(MZ)倍数: {alpha_ratio}")
    print(f"  标度因子: {scale_factor}")
    if args.ptmin is not None and args.ptmax is not None:
        print(f"  pT范围: {args.ptmin}-{args.ptmax} GeV, 分为 {args.pt_bins} 个区间")

    try:
        # 读取拟合参数
        fit, fit_NLO, pt_center_from_eec, eec_params = read_eec_extract_result(eec_result_file)
        print(f"\n成功读取EEC结果文件: {eec_result_file}")
        print(f"LO拟合参数: {fit}")
        print(f"NLO拟合参数: {fit_NLO}")
        print(f"pT中点: {pt_center_from_eec} GeV")
        print(f"其他参数: {eec_params}")

        # 使用结果文件中的参数
        Ecom = eec_params['Ecom']
        radius = eec_params['radius']
        eta_range = eec_params['eta_range']

        # 设置pT区间
        if args.ptmin is not None and args.ptmax is not None:
            # 使用命令行指定的pT范围，分割成多个区间
            pt_bins = np.linspace(args.ptmin, args.ptmax, args.pt_bins + 1)
            pt_sub_ranges = [(pt_bins[i], pt_bins[i + 1]) for i in range(len(pt_bins) - 1)]
        else:
            # 使用结果文件中的pT中点构造单个区间
            pt_center = pt_center_from_eec
            pt_sub_ranges = [(pt_center - 10, pt_center + 10)]  # ±10 GeV范围

        print(f"\n使用的物理参数:")
        print(f"  质心系能量: {Ecom} GeV")
        print(f"  喷注半径: {radius}")
        print(f"  eta范围: {eta_range}")
        print(f"  pT区间数: {len(pt_sub_ranges)}")
        for i, pt_range in enumerate(pt_sub_ranges):
            print(f"    区间{i+1}: {pt_range[0]:.1f}-{pt_range[1]:.1f} GeV")

    except Exception as e:
        print(f"读取EEC结果文件失败: {e}")
        print("使用默认拟合参数和物理参数")
        # 使用默认值
        fit = np.array([0.004605, 1.18525414])
        fit_NLO = np.array([0.02278587, 1.05557197])
        Ecom = 13600
        radius = 0.4
        eta_range = (-1.0, 1.0)

        if args.ptmin is not None and args.ptmax is not None:
            pt_bins = np.linspace(args.ptmin, args.ptmax, args.pt_bins + 1)
            pt_sub_ranges = [(pt_bins[i], pt_bins[i + 1]) for i in range(len(pt_bins) - 1)]
        else:
            pt_sub_ranges = [(30, 60)]

    # 计算调整后的alpha_s(MZ)
    base_als_mz = 0.118
    current_als_mz = base_als_mz * alpha_ratio

    print(f"\n计算参数:")
    print(f"  基础alpha_s(MZ): {base_als_mz}")
    print(f"  调整倍数: {alpha_ratio}")
    print(f"  实际alpha_s(MZ): {current_als_mz}")
    print(f"  标度因子: {scale_factor}")

    try:
        print(f"\n{'='*50}")
        print(f"开始EEC演化计算")
        print(f"ALPHA_S(MZ) = {current_als_mz:.6f} (基础值 × {alpha_ratio:.3f})")
        print(f"标度因子 = {scale_factor}")
        print(f"{'='*50}")

        calculator = PPJetCalculator(
            pdf_name=args.pdf_name, ALS_MZ=current_als_mz
        )

        # 生成输出文件名
        filename_all = f"{eec_result_file}_EVO_Mz{alpha_ratio:.2f}_r{scale_factor:.1f}.txt"

        with open(filename_all, "w") as f_all:
            # 写入文件头部信息
            f_all.write(f"# EEC演化计算结果\n")
            f_all.write(f"# 输入文件: {eec_result_file}\n")
            f_all.write(f"# alpha_s(MZ): {current_als_mz:.6f} (倍数: {alpha_ratio})\n")
            f_all.write(f"# 标度因子: {scale_factor}\n")
            f_all.write(f"# PDF集: {args.pdf_name}\n")
            f_all.write(f"# 物理参数: Ecom={Ecom}, radius={radius}, eta_range={eta_range}\n")
            f_all.write(f"# LO拟合参数: {fit}\n")
            f_all.write(f"# NLO拟合参数: {fit_NLO}\n")
            f_all.write(f"#\n")

            header = "# pT_center\tEEC_LL\tEEC_NLL\tEEC_NNLL\n"
            f_all.write(header)

            # 循环处理所有pT区间
            for i, pt_range in enumerate(pt_sub_ranges):
                pt_center = (pt_range[0] + pt_range[1]) / 2.0
                print(f"\n--- 计算 pT 区间 {i+1}/{len(pt_sub_ranges)}: {pt_range[0]:.1f}-{pt_range[1]:.1f} GeV ---")
                print(f"pT中点: {pt_center:.1f} GeV")

                result = calculator.calculate(
                    Ecom=Ecom,
                    Q0=pt_center,
                    radius=radius,
                    pT_range=pt_range,
                    eta_range=eta_range,
                    scale_factors=(scale_factor, scale_factor),
                    vegas_params={"nitn": 15, "neval": 50000, "nproc": 16},
                )

                result_for_bin = result.flatten()
                print(f"原始计算结果 (前4个分量): {[result_for_bin[j].mean for j in range(4)]}")

                cs_q = result_for_bin[12]
                cs_g = result_for_bin[13]
                cs_q_NLO = result_for_bin[14]
                cs_g_NLO = result_for_bin[15]

                total_cs = cs_q + cs_g
                print(f"截面结果: 夸克={cs_q.mean:.6e}, 胶子={cs_g.mean:.6e}, 总计={total_cs.mean:.6e}")

                total_cs_NLO = cs_q_NLO + cs_g_NLO
                if total_cs.mean != 0:
                    eec_ll_all = (
                        fit[0] * (result_for_bin[0] + result_for_bin[2])
                        + fit[1] * (result_for_bin[1] + result_for_bin[3])
                    ) / total_cs
                    eec_nll_all = (
                        fit_NLO[0] * (result_for_bin[4] + result_for_bin[6])
                        + fit_NLO[1] * (result_for_bin[5] + result_for_bin[7])
                    ) / total_cs
                    eec_nnll_all = (
                        fit_NLO[0] * (result_for_bin[8] + result_for_bin[10])
                        + fit_NLO[1] * (result_for_bin[9] + result_for_bin[11])
                    ) / total_cs

                    print(f"EEC结果: LL={eec_ll_all.mean:.6e}, NLL={eec_nll_all.mean:.6e}, NNLL={eec_nnll_all.mean:.6e}")

                    f_all.write(
                        f"{pt_center:.2f}\t{eec_ll_all.mean:.6e}\t{eec_nll_all.mean:.6e}\t{eec_nnll_all.mean:.6e}\n"
                    )

                    f_all.flush()
                else:
                    print("警告: 总截面为零，跳过EEC计算")

                print(f"pT区间 {i+1} 完成")

        print(f"\n{'='*50}")
        print(f"所有计算完成!")
        print(f"输出文件: {filename_all}")
        print(f"参数: alpha_s(MZ)={current_als_mz:.6f}, 标度因子={scale_factor}")
        print(f"处理了 {len(pt_sub_ranges)} 个pT区间")
        print(f"{'='*50}")

        print(f"\n\n{'='*30}")
        print("所有计算任务已完成！")

    except Exception as e:
        import traceback

        traceback.print_exc()
        print(f"\n程序执行时发生错误: {e}")
