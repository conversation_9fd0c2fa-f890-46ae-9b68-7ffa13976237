# config.yaml
# ----------------------------------------------------------------------
# 全局通用配置 (Global Settings)
# ----------------------------------------------------------------------
# 这些设置被所有模式共享。

global_settings:
  # 最大并行任务数 (建议设置为你的 CPU 核心数)
  max_parallel_jobs: 10

  # 科学计算程序的可执行文件路径
  executable_path: "/home/<USER>/Project/C/EC/build/bin/Pythia_EEC_intra_jet_log"

  # executable_path: "/home/<USER>/Project/C/EC/build/bin/Pythia_EEC_intra_jet_CMS"
  

  # 所有输出数据的根目录
  base_output_directory: "/home/<USER>/Project/Data/EEC/raw"

  # 所有模式共享的、固定的物理参数
  # 这些参数会被模式中的 fixed_params 或 parameter_sets 覆盖
  shared_parameters:
    chflag: 0
    pow1: 1.0
    pow2: 1.0
    chgflag: 0
    n_pt_bins: 1
    eta: 0.5
    nevent: 1000000