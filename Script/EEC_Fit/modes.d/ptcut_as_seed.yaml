# ptcut_as_seed.yaml (放入 modes.d/ 文件夹)

ptcut_as_seed:
  description: "对每个随机种子，扫描一组预设的 ptcut 范围 (elow, eupp 对), alpha_s值, Seed值"
  
  output_subdirectory: "scan_ptcut_alpha_Seed-fixed_Ecom_{Ecom}"

  # --- 参数扫描定义 ---
  scan_parameters:
    alpha_s:
      values: [0.118] # <-- 使用 'values' 明确指定列表
    Seed:
      range: [1000,1010,1]           # <-- 使用 'range' 明确指定范围

  # parameter_sets:
  #       - { elow: 20, eupp: 100}
  # fixed_params:
  #   # Ecom: 5020
  #   Ecom: 13600
  parameter_sets:
        # - { elow: 1410, eupp: 1784}
        # - { elow: 468, eupp: 638 }
        # - { elow: 330, eupp: 468 }
        # - { elow: 97, eupp: 220 }
        # - { elow: 1101, eupp: 1410 }
        # - { elow: 220, eupp: 330 }
        # - { elow: 638, eupp: 846 }
        # - { elow: 846, eupp: 1101 }
        - { elow: 20, eupp: 40}
        - { elow: 40, eupp: 60}
        - { elow: 60, eupp: 80}
  fixed_params:
    Ecom: 5020